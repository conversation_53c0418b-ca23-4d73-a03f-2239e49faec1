import 'package:get/get.dart';
import 'package:gt_plus/enums/questionnaire_type_enum.dart';
import 'package:gt_plus/utils/reusableWidgets/resusable_snackbar.dart';
import 'base_questionnaire_controller.dart';
import '../view/tfi_questionnaire_view.dart';

class TFIQuestionnaireController extends BaseQuestionnaireController {
  @override
  QuestionnaireType get questionnaireType => QuestionnaireType.tfiQuest;

  @override
  String get questionnaireKey => 'setThree'; // TFI is in setThree based on the demo data

  @override
  String get routeName => TFIQuestionnaireView.routeName;

  // Observable for tracking meta question answer
  final Rxn<bool> metaQuestionAnswer = Rxn<bool>();

  // Observable for showing detailed questions
  final RxBool showDetailedQuestions = false.obs;

  void updateMetaAnswer(bool hasExperienceTinnitus) {
    metaQuestionAnswer.value = hasExperienceTinnitus;
    updateAnswer('metaQuest', hasExperienceTinnitus);

    if (hasExperienceTinnitus) {
      // User has tinnitus, show detailed questions
      showDetailedQuestions.value = true;
    } else {
      // User doesn't have tinnitus, hide detailed questions and clear any previous answers
      showDetailedQuestions.value = false;
      _clearDetailedAnswers();
    }
  }

  void _clearDetailedAnswers() {
    // Clear all answers except the meta question answer
    final metaAnswer = answers['metaQuest'];
    answers.clear();
    answers['metaQuest'] = metaAnswer;
    answers.refresh();
  }

  @override
  bool validateAnswers() {
    // First check if meta question is answered
    if (metaQuestionAnswer.value == null) {
      reusableSnackBar(message: 'Please answer the tinnitus experience question');
      return false;
    }

    // If user doesn't have tinnitus, only meta question needs to be answered
    if (metaQuestionAnswer.value == false) {
      return true;
    }

    // If user has tinnitus, validate only the detailed questions that are shown
    final questionnaireSet = getQuestionnaireSet();
    if (questionnaireSet == null) return false;

    // Check if all required detailed questions are answered
    if (questionnaireSet.data != null) {
      for (final section in questionnaireSet.data!) {
        if (section.questions != null) {
          for (final question in section.questions!) {
            if (!isQuestionAnswered(question.value)) {
              reusableSnackBar(message: 'Please answer all questions');
              return false;
            }
          }
        }
      }
    }

    return true;
  }

  @override
  Future<void> loadSavedAnswers() async {
    await super.loadSavedAnswers();

    // Load saved meta answer
    final savedMetaAnswer = getAnswer('metaQuest');
    if (savedMetaAnswer != null && savedMetaAnswer is bool) {
      metaQuestionAnswer.value = savedMetaAnswer;
      showDetailedQuestions.value = savedMetaAnswer;
    }
  }

  @override
  void onFormReset() {
    // Reset TFI-specific state
    metaQuestionAnswer.value = null;
    showDetailedQuestions.value = false;
  }
}
